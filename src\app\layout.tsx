import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import Navigation from "@/components/navigation";
import Footer from "@/components/footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Portfolio - Full-Stack Developer & UX/UI Designer",
  description: "Professional portfolio showcasing full-stack development and UX/UI design projects. Creating beautiful, functional digital experiences.",
  keywords: ["full-stack developer", "UX/UI designer", "web development", "portfolio", "frontend", "backend", "design"],
  authors: [{ name: "Your Name" }],
  openGraph: {
    title: "Portfolio - Full-Stack Developer & UX/UI Designer",
    description: "Professional portfolio showcasing full-stack development and UX/UI design projects",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Portfolio - Full-Stack Developer & UX/UI Designer",
    description: "Professional portfolio showcasing full-stack development and UX/UI design projects",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground min-h-screen flex flex-col`}
      >
        <Navigation />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
        <Toaster />
      </body>
    </html>
  );
}
