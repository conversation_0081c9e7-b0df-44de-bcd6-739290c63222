"use client";

import React from 'react';
import { Github, Linkedin, Twitter, Instagram, Mail, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface SocialMediaIconsProps {
  className?: string;
  variant?: 'default' | 'compact' | 'large';
  showLabels?: boolean;
}

const socialLinks = [
  {
    name: 'GitHub',
    icon: Github,
    url: 'https://github.com/yourusername',
    color: 'hover:text-neon-blue hover:shadow-[0_0_20px_rgba(0,212,255,0.5)]',
    bgColor: 'hover:bg-neon-blue/10'
  },
  {
    name: 'LinkedIn',
    icon: Linkedin,
    url: 'https://linkedin.com/in/yourusername',
    color: 'hover:text-neon-purple hover:shadow-[0_0_20px_rgba(185,103,255,0.5)]',
    bgColor: 'hover:bg-neon-purple/10'
  },
  {
    name: 'Twitter',
    icon: Twitter,
    url: 'https://twitter.com/yourusername',
    color: 'hover:text-neon-green hover:shadow-[0_0_20px_rgba(57,255,20,0.5)]',
    bgColor: 'hover:bg-neon-green/10'
  },
  {
    name: 'Instagram',
    icon: Instagram,
    url: 'https://instagram.com/yourusername',
    color: 'hover:text-neon-pink hover:shadow-[0_0_20px_rgba(255,0,110,0.5)]',
    bgColor: 'hover:bg-neon-pink/10'
  },
  {
    name: 'Email',
    icon: Mail,
    url: 'mailto:<EMAIL>',
    color: 'hover:text-neon-blue hover:shadow-[0_0_20px_rgba(0,212,255,0.5)]',
    bgColor: 'hover:bg-neon-blue/10'
  },
  {
    name: 'Website',
    icon: Globe,
    url: 'https://yourwebsite.com',
    color: 'hover:text-neon-purple hover:shadow-[0_0_20px_rgba(185,103,255,0.5)]',
    bgColor: 'hover:bg-neon-purple/10'
  }
];

export default function SocialMediaIcons({ 
  className, 
  variant = 'default', 
  showLabels = false 
}: SocialMediaIconsProps) {
  const getIconSize = () => {
    switch (variant) {
      case 'compact': return 'h-4 w-4';
      case 'large': return 'h-8 w-8';
      default: return 'h-5 w-5';
    }
  };

  const getButtonSize = () => {
    switch (variant) {
      case 'compact': return 'h-8 w-8';
      case 'large': return 'h-14 w-14';
      default: return 'h-10 w-10';
    }
  };

  return (
    <div className={cn("flex items-center gap-3", className)}>
      {socialLinks.map((social, index) => {
        const Icon = social.icon;
        return (
          <Button
            key={social.name}
            variant="ghost"
            size="icon"
            asChild
            className={cn(
              getButtonSize(),
              "relative group rounded-full border border-glass-border bg-glass-bg/30 backdrop-blur-sm",
              "text-cyber-muted transition-all duration-300 ease-out",
              "hover:scale-110 hover:border-current hover:bg-glass-bg/50",
              social.color,
              social.bgColor,
              "animate-fadeIn",
              `animation-delay-${index * 100}`
            )}
          >
            <a
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-full h-full"
              aria-label={social.name}
            >
              <Icon className={cn(getIconSize(), "transition-all duration-300")} />
              
              {/* Cyber glow effect */}
              <div className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-transparent via-current/20 to-transparent animate-pulse" />
              
              {/* Scanning line effect */}
              <div className="absolute inset-0 rounded-full overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
              </div>
              
              {showLabels && (
                <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs cyber-mono-alt text-cyber-muted opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                  {social.name}
                </span>
              )}
            </a>
          </Button>
        );
      })}
    </div>
  );
}

// Animated version with staggered entrance
export function AnimatedSocialMediaIcons({ 
  className, 
  variant = 'default', 
  showLabels = false,
  delay = 0 
}: SocialMediaIconsProps & { delay?: number }) {
  return (
    <div className={cn("flex items-center gap-3", className)}>
      {socialLinks.map((social, index) => {
        const Icon = social.icon;
        return (
          <Button
            key={social.name}
            variant="ghost"
            size="icon"
            asChild
            className={cn(
              variant === 'compact' ? 'h-8 w-8' : variant === 'large' ? 'h-14 w-14' : 'h-10 w-10',
              "relative group rounded-full border border-glass-border bg-glass-bg/30 backdrop-blur-sm",
              "text-cyber-muted transition-all duration-500 ease-out",
              "hover:scale-110 hover:border-current hover:bg-glass-bg/50",
              "opacity-0 translate-y-4 animate-fadeInUp",
              social.color,
              social.bgColor
            )}
            style={{
              animationDelay: `${delay + index * 150}ms`,
              animationFillMode: 'forwards'
            }}
          >
            <a
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center w-full h-full"
              aria-label={social.name}
            >
              <Icon className={cn(
                variant === 'compact' ? 'h-4 w-4' : variant === 'large' ? 'h-8 w-8' : 'h-5 w-5',
                "transition-all duration-300"
              )} />
              
              {/* Enhanced cyber effects */}
              <div className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute inset-0 rounded-full bg-current/10 animate-ping" />
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-transparent via-current/30 to-transparent animate-pulse" />
              </div>
              
              {/* Matrix-style scanning effect */}
              <div className="absolute inset-0 rounded-full overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-in-out" />
              </div>
              
              {showLabels && (
                <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs cyber-mono-alt text-cyber-muted opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap bg-glass-bg/80 px-2 py-1 rounded backdrop-blur-sm">
                  {social.name}
                </span>
              )}
            </a>
          </Button>
        );
      })}
    </div>
  );
}
