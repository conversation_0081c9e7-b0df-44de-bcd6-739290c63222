"use client";

import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface AnimatedTextProps {
  text: string;
  className?: string;
  animation?: 'typewriter' | 'glitch' | 'neon-flicker' | 'hologram' | 'fade-in-words' | 'slide-up-words';
  delay?: number;
  speed?: number;
}

export default function AnimatedText({ 
  text, 
  className = '', 
  animation = 'typewriter',
  delay = 0,
  speed = 50
}: AnimatedTextProps) {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  useEffect(() => {
    if (!isVisible) return;

    if (animation === 'typewriter') {
      if (currentIndex < text.length) {
        const timer = setTimeout(() => {
          setDisplayText(text.slice(0, currentIndex + 1));
          setCurrentIndex(currentIndex + 1);
        }, speed);

        return () => clearTimeout(timer);
      }
    } else if (animation === 'fade-in-words' || animation === 'slide-up-words') {
      setDisplayText(text);
    } else {
      setDisplayText(text);
    }
  }, [currentIndex, text, animation, speed, isVisible]);

  if (animation === 'typewriter') {
    return (
      <span className={cn('text-typewriter', className)}>
        {displayText}
      </span>
    );
  }

  if (animation === 'glitch') {
    return (
      <span 
        className={cn('text-glitch', className)} 
        data-text={text}
      >
        {text}
      </span>
    );
  }

  if (animation === 'neon-flicker') {
    return (
      <span className={cn('text-neon-flicker', className)}>
        {text}
      </span>
    );
  }

  if (animation === 'hologram') {
    return (
      <span className={cn('text-hologram', className)}>
        {text}
      </span>
    );
  }

  if (animation === 'fade-in-words') {
    const words = text.split(' ');
    return (
      <span className={className}>
        {words.map((word, index) => (
          <span
            key={index}
            className={cn(
              'inline-block opacity-0 animate-fadeIn',
              `stagger-${Math.min(index + 1, 8)}`
            )}
            style={{ animationDelay: `${delay + index * 100}ms` }}
          >
            {word}
            {index < words.length - 1 && ' '}
          </span>
        ))}
      </span>
    );
  }

  if (animation === 'slide-up-words') {
    const words = text.split(' ');
    return (
      <span className={className}>
        {words.map((word, index) => (
          <span
            key={index}
            className={cn(
              'inline-block opacity-0 animate-fadeInUp',
              `stagger-${Math.min(index + 1, 8)}`
            )}
            style={{ animationDelay: `${delay + index * 150}ms` }}
          >
            {word}
            {index < words.length - 1 && ' '}
          </span>
        ))}
      </span>
    );
  }

  return <span className={className}>{text}</span>;
}

// Component for creating a matrix-style text reveal effect
export function MatrixText({ 
  text, 
  className = '',
  revealSpeed = 100 
}: { 
  text: string; 
  className?: string; 
  revealSpeed?: number;
}) {
  const [revealedText, setRevealedText] = useState('');
  const [isRevealing, setIsRevealing] = useState(false);
  
  const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';

  useEffect(() => {
    if (!isRevealing) return;

    let currentIndex = 0;
    const interval = setInterval(() => {
      if (currentIndex <= text.length) {
        const revealed = text.slice(0, currentIndex);
        const scrambled = Array.from({ length: text.length - currentIndex }, () =>
          chars[Math.floor(Math.random() * chars.length)]
        ).join('');
        
        setRevealedText(revealed + scrambled);
        currentIndex++;
      } else {
        setRevealedText(text);
        clearInterval(interval);
      }
    }, revealSpeed);

    return () => clearInterval(interval);
  }, [isRevealing, text, revealSpeed, chars]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsRevealing(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <span className={cn('cyber-mono font-mono', className)}>
      {revealedText || text}
    </span>
  );
}

// Component for creating a scanning text effect
export function ScanningText({ 
  text, 
  className = '',
  scanSpeed = 2000 
}: { 
  text: string; 
  className?: string; 
  scanSpeed?: number;
}) {
  const [scanPosition, setScanPosition] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setScanPosition(prev => (prev + 1) % (text.length + 10));
    }, scanSpeed / text.length);

    return () => clearInterval(interval);
  }, [text.length, scanSpeed]);

  return (
    <span className={cn('relative inline-block', className)}>
      {text.split('').map((char, index) => (
        <span
          key={index}
          className={cn(
            'transition-all duration-200',
            Math.abs(index - scanPosition) < 3
              ? 'text-neon-blue text-glow'
              : 'text-secondary'
          )}
        >
          {char}
        </span>
      ))}
      <span
        className="absolute top-0 w-0.5 h-full bg-neon-blue animate-pulse"
        style={{
          left: `${(scanPosition / text.length) * 100}%`,
          boxShadow: '0 0 10px var(--neon-blue)'
        }}
      />
    </span>
  );
}
