"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ExternalLink, Github, Search, Filter, Zap, Eye } from "lucide-react";
import Particles from "@/components/particles";

export default function Projects() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { id: "all", name: "ALL SYSTEMS" },
    { id: "web", name: "WEB INTERFACE" },
    { id: "mobile", name: "MOBILE APPS" },
    { id: "design", name: "HOLOGRAPHIC UI" },
    { id: "fullstack", name: "NEURAL NETWORKS" },
  ];

  const projects = [
    {
      id: 1,
      title: "NEON COMMERCE",
      description: "Full-stack e-commerce solution with cyberpunk aesthetics and real-time inventory management. Features include neural network recommendations, holographic product displays, and quantum encryption.",
      image: "/api/placeholder/600/400",
      category: "fullstack",
      technologies: ["Next.js", "TypeScript", "MongoDB", "Stripe", "Socket.io", "AI"],
      githubUrl: "https://github.com/yourusername/neon-commerce",
      liveUrl: "https://neon-commerce-demo.com",
      featured: true,
    },
    {
      id: 2,
      title: "CYBER TASK MANAGER",
      description: "Collaborative task management tool with holographic UI, neural sync capabilities, and real-time collaborative editing. Features include AI-powered task prioritization and team neural networks.",
      image: "/api/placeholder/600/400",
      category: "web",
      technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Socket.io", "PostgreSQL", "WebRTC"],
      githubUrl: "https://github.com/yourusername/cyber-task-manager",
      liveUrl: "https://cyber-task-demo.com",
      featured: true,
    },
    {
      id: 3,
      title: "NEXUS BANKING UI",
      description: "Modern mobile banking interface with biometric authentication, neural security protocols, and holographic transaction displays. Features include quantum encryption and real-time fraud detection.",
      image: "/api/placeholder/600/400",
      category: "mobile",
      technologies: ["React Native", "Figma", "Blockchain", "3D Graphics", "Biometrics", "AI"],
      githubUrl: "https://github.com/yourusername/nexus-banking",
      liveUrl: "https://nexus-banking-demo.com",
      featured: true,
    },
    {
      id: 4,
      title: "CYBER PORTFOLIO",
      description: "Responsive portfolio website showcasing design and development skills with neon aesthetics, particle systems, and holographic effects. Built with Next.js and cyberpunk design principles.",
      image: "/api/placeholder/600/400",
      category: "web",
      technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Framer Motion", "Canvas API"],
      githubUrl: "https://github.com/yourusername/cyber-portfolio",
      liveUrl: "https://cyber-portfolio-demo.com",
      featured: false,
    },
    {
      id: 5,
      title: "QUANTUM WEATHER DASHBOARD",
      description: "Real-time weather dashboard with location-based forecasts, interactive holographic maps, and detailed weather analytics. Features neural network predictions and 3D weather visualization.",
      image: "/api/placeholder/600/400",
      category: "web",
      technologies: ["React", "Three.js", "Weather API", "TensorFlow.js", "WebGL", "D3.js"],
      githubUrl: "https://github.com/yourusername/quantum-weather",
      liveUrl: "https://quantum-weather-demo.com",
      featured: false,
    },
    {
      id: 6,
      title: "NEURAL FITNESS TRACKER",
      description: "Comprehensive fitness tracking application with workout planning, biometric monitoring, and social features. Includes mobile and web versions with neural network coaching.",
      image: "/api/placeholder/600/400",
      category: "mobile",
      technologies: ["React Native", "Firebase", "Redux", "Health APIs", "Machine Learning", "Wearables"],
      githubUrl: "https://github.com/yourusername/neural-fitness",
      liveUrl: "https://neural-fitness-demo.com",
      featured: false,
    },
    {
      id: 7,
      title: "HOLO DESIGN SYSTEM",
      description: "Comprehensive design system with holographic components, style guides, and documentation. Ensures consistency across multiple projects with cyberpunk aesthetics.",
      image: "/api/placeholder/600/400",
      category: "design",
      technologies: ["Figma", "Storybook", "React", "CSS Variables", "Animation Libraries", "3D Tools"],
      githubUrl: "https://github.com/yourusername/holo-design-system",
      liveUrl: "https://holo-design-demo.com",
      featured: false,
    },
    {
      id: 8,
      title: "CYBER BLOG PLATFORM",
      description: "Modern blog platform with markdown support, SEO optimization, and holographic content management. Built for performance and scalability with neural content recommendations.",
      image: "/api/placeholder/600/400",
      category: "fullstack",
      technologies: ["Next.js", "MDX", "TypeScript", "Tailwind CSS", "Vercel", "AI Content"],
      githubUrl: "https://github.com/yourusername/cyber-blog",
      liveUrl: "https://cyber-blog-demo.com",
      featured: false,
    },
  ];

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || project.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const featuredProjects = projects.filter(project => project.featured);

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Particle Background */}
      <Particles />
      
      {/* Animated Grid Background */}
      <div className="fixed inset-0 pointer-events-none z-1">
        <div className="absolute inset-0 opacity-20">
          <div className="h-full w-full" style={{
            backgroundImage: `
              linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'grid-move 20s linear infinite'
          }} />
        </div>
      </div>

      <div className="relative z-10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <span className="tech-tag inline-block mb-4">PROJECT ARCHIVE</span>
            <h1 className="text-4xl lg:text-5xl font-bold cyber-heading gradient-text mb-4">
              CYBER PROJECTS
            </h1>
            <p className="cyber-body text-secondary max-w-3xl mx-auto">
              Holographic projections of my digital constructs and neural interfaces. 
              Each project represents a unique challenge solved with cutting-edge technology.
            </p>
          </div>

          {/* Featured Projects */}
          <section className="mb-20">
            <div className="text-center mb-12">
              <span className="tech-tag inline-block mb-4">FEATURED MODULES</span>
              <h2 className="text-3xl lg:text-4xl font-bold cyber-heading gradient-text mb-4">
                PRIME SYSTEMS
              </h2>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {featuredProjects.map((project) => (
                <Card key={project.id} className="glass-card hover-lift relative overflow-hidden">
                  <div className="scan-line"></div>
                  <CardHeader className="p-0">
                    <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 rounded-t-xl flex items-center justify-center relative">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="relative z-10 text-center">
                        <Eye className="h-8 w-8 text-primary mx-auto mb-2" />
                        <span className="cyber-mono text-primary">HOLOGRAPHIC DISPLAY</span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="secondary" className="tech-tag">
                        {categories.find(cat => cat.id === project.category)?.name}
                      </Badge>
                      <Badge variant="outline" className="tech-tag border-neon-pink text-neon-pink">
                        FEATURED
                      </Badge>
                    </div>
                    <CardTitle className="mb-2 cyber-subheading text-primary hover-glow">
                      {project.title}
                    </CardTitle>
                    <CardDescription className="cyber-body text-secondary mb-4">
                      {project.description}
                    </CardDescription>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.technologies.slice(0, 3).map((tech, index) => (
                        <Badge key={index} className="tech-tag">
                          {tech}
                        </Badge>
                      ))}
                      {project.technologies.length > 3 && (
                        <Badge className="tech-tag">
                          +{project.technologies.length - 3}
                        </Badge>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button asChild variant="outline" size="sm" className="cyber-button flex-1">
                        <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                          <Zap className="mr-2 h-4 w-4" />
                          ACCESS
                        </a>
                      </Button>
                      <Button asChild variant="outline" size="sm" className="cyber-button">
                        <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                          <Github className="h-4 w-4" />
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          {/* All Projects */}
          <section>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
              <div>
                <span className="tech-tag inline-block mb-4">SYSTEM DATABASE</span>
                <h2 className="text-3xl font-bold cyber-heading gradient-text">
                  ALL PROJECTS
                </h2>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary h-4 w-4" />
                  <Input
                    placeholder="Search projects..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-full sm:w-64 bg-transparent border-cyan-500/30 text-primary placeholder:text-secondary"
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-full sm:w-48 bg-transparent border-cyan-500/30 text-primary">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProjects.map((project) => (
                <Card key={project.id} className="glass-card hover-lift relative overflow-hidden">
                  <div className="scan-line"></div>
                  <CardHeader className="p-0">
                    <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 rounded-t-xl flex items-center justify-center">
                      <span className="cyber-mono text-primary">PROJECT DATA</span>
                    </div>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="secondary" className="tech-tag">
                        {categories.find(cat => cat.id === project.category)?.name}
                      </Badge>
                      {project.featured && (
                        <Badge variant="outline" className="tech-tag border-neon-pink text-neon-pink">
                          FEATURED
                        </Badge>
                      )}
                    </div>
                    <CardTitle className="mb-2 cyber-subheading text-primary hover-glow">
                      {project.title}
                    </CardTitle>
                    <CardDescription className="cyber-body text-secondary mb-4 line-clamp-3">
                      {project.description}
                    </CardDescription>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.technologies.slice(0, 4).map((tech, index) => (
                        <Badge key={index} className="tech-tag">
                          {tech}
                        </Badge>
                      ))}
                      {project.technologies.length > 4 && (
                        <Badge className="tech-tag">
                          +{project.technologies.length - 4}
                        </Badge>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button asChild variant="outline" size="sm" className="cyber-button flex-1">
                        <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                          <Zap className="mr-2 h-4 w-4" />
                          ACCESS
                        </a>
                      </Button>
                      <Button asChild variant="outline" size="sm" className="cyber-button">
                        <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                          <Github className="h-4 w-4" />
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredProjects.length === 0 && (
              <div className="text-center py-12">
                <Card className="glass-card max-w-md mx-auto">
                  <CardContent className="p-8">
                    <h3 className="cyber-subheading text-primary mb-2">NO MATCHES FOUND</h3>
                    <p className="cyber-body text-secondary mb-4">
                      No projects found matching your search criteria.
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSearchTerm("");
                        setSelectedCategory("all");
                      }}
                      className="cyber-button"
                    >
                      RESET FILTERS
                    </Button>
                  </CardContent>
                </Card>
              </div>
            )}
          </section>
        </div>
      </div>
    </div>
  );
}