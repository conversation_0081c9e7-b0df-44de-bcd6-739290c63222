"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowR<PERSON>, Code, Palette, ExternalLink, Zap } from "lucide-react";
import Particles from "@/components/particles";
import AnimatedBackground from "@/components/animated-background";
import AnimatedText, { MatrixText, ScanningText } from "@/components/animated-text";
import { useScrollAnimation } from "@/hooks/use-scroll-animation";

export default function Home() {
  // Sample data - replace with your actual information
  const featuredProjects = [
    {
      title: "NEON COMMERCE",
      description: "Full-stack e-commerce platform with cyberpunk aesthetics and real-time inventory",
      technologies: ["Next.js", "TypeScript", "MongoDB", "Stripe", "Socket.io"],
      image: "/api/placeholder/400/250",
      link: "/projects",
    },
    {
      title: "CYBER TASK MANAGER",
      description: "Collaborative task management with holographic UI and neural sync",
      technologies: ["React", "Node.js", "PostgreSQL", "WebRTC", "AI"],
      image: "/api/placeholder/400/250",
      link: "/projects",
    },
    {
      title: "NEXUS BANKING UI",
      description: "Futuristic banking interface with biometric auth and neural security",
      technologies: ["React Native", "Figma", "Blockchain", "3D Graphics"],
      image: "/api/placeholder/400/250",
      link: "/projects",
    },
  ];

  const skills = [
    { name: "NEURAL DEVELOPMENT", level: 95, icon: Code },
    { name: "CYBER UX/UI", level: 90, icon: Palette },
    { name: "QUANTUM BACKEND", level: 85, icon: Zap },
    { name: "HOLO INTERFACE", level: 80, icon: Zap },
  ];

  const { ref: heroRef, isVisible: heroVisible } = useScrollAnimation();
  const { ref: projectsRef, isVisible: projectsVisible } = useScrollAnimation();
  const { ref: skillsRef, isVisible: skillsVisible } = useScrollAnimation();

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Subtle Background Layers */}
      <Particles />
      <AnimatedBackground variant="circuit" intensity="low" />

      {/* Animated Grid Background */}
      <div className="fixed inset-0 pointer-events-none z-1">
        <div className="absolute inset-0 opacity-20">
          <div className="h-full w-full" style={{
            backgroundImage: `
              linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'grid-move 20s linear infinite'
          }} />
        </div>
      </div>

      {/* Enhanced Hero Section */}
      <section ref={heroRef} className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Enhanced Hero Effects */}
        <div className="hero-glow animate-energy-wave"></div>
        <div className="scan-line"></div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-2 h-2 bg-neon-blue rounded-full animate-float opacity-60"></div>
        <div className="absolute top-40 right-20 w-1 h-1 bg-neon-pink rounded-full animate-float opacity-40" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-1/4 w-1.5 h-1.5 bg-neon-green rounded-full animate-float opacity-50" style={{ animationDelay: '4s' }}></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className={`transition-all duration-1000 ${heroVisible ? 'animate-fadeInUp' : 'opacity-0 translate-y-10'}`}>
              <div className="mb-6">
                <MatrixText
                  text="SYSTEM ONLINE"
                  className="tech-tag inline-block mb-4"
                />
              </div>
              <h1 className="text-4xl lg:text-6xl font-bold cyber-heading-alt mb-6">
                <AnimatedText
                  text="CYBER"
                  animation="glitch"
                  className="gradient-text block"
                />
                <AnimatedText
                  text="DEVELOPER"
                  animation="neon-flicker"
                  className="text-cyber-secondary block"
                  delay={1000}
                />
              </h1>
              <div className="text-xl lg:text-2xl cyber-body-alt text-cyber-accent mb-4">
                <ScanningText text="[Neural Interface Designer]" />
              </div>
              <p className="cyber-body-alt text-cyber-muted mb-10 max-w-lg">
                <AnimatedText
                  text="Crafting digital experiences in the neon-lit corridors of cyberspace. Full-stack architect and UX designer building the future, one pixel at a time."
                  animation="fade-in-words"
                  delay={2000}
                />
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg" className="cyber-button text-lg hover-neon">
                  <Link href="/projects">
                    <Zap className="mr-2 h-5 w-5" />
                    ACCESS PORTFOLIO
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="cyber-button text-lg hover-tilt">
                  <Link href="/contact">
                    <ArrowRight className="mr-2 h-5 w-5" />
                    INITIATE CONTACT
                  </Link>
                </Button>
              </div>
            </div>
            <div className={`transition-all duration-1000 delay-500 ${heroVisible ? 'animate-scaleIn' : 'opacity-0 scale-90'}`}>
              <div className="glass-card p-8 relative overflow-hidden hover-lift perspective-1000">
                <div className="scan-line"></div>
                <div className="aspect-square bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center relative backdrop-blur-strong">
                  <div className="text-center z-10 transform-3d">
                    <div className="w-32 h-32 mx-auto mb-4 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center animate-pulse-glow hover-tilt">
                      <span className="text-4xl font-bold cyber-heading-alt text-white animate-hologram-flicker">YN</span>
                    </div>
                    <p className="cyber-mono-alt text-cyber-secondary animate-neon-pulse">NEURAL AVATAR</p>
                    <div className="mt-4">
                      <span className="tech-tag animate-data-stream text-cyber-highlight">STATUS: ACTIVE</span>
                    </div>
                  </div>
                  {/* Holographic effect overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-scan opacity-30"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Featured Projects */}
      <section ref={projectsRef} className="py-20 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center mb-16 transition-all duration-1000 ${projectsVisible ? 'animate-fadeInUp' : 'opacity-0 translate-y-10'}`}>
            <MatrixText
              text="FEATURED MODULES"
              className="tech-tag inline-block mb-4"
            />
            <h2 className="text-3xl lg:text-4xl font-bold cyber-heading-alt gradient-text mb-4">
              <AnimatedText
                text="CYBER PROJECTS"
                animation="hologram"
              />
            </h2>
            <p className="cyber-body-alt text-cyber-muted max-w-2xl mx-auto">
              <AnimatedText
                text="Holographic projections of my latest digital constructs and neural interfaces."
                animation="fade-in-words"
                delay={500}
              />
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProjects.map((project, index) => (
              <Card
                key={index}
                className={`glass-card hover-lift interactive-card transition-all duration-700 ${
                  projectsVisible ? `animate-bounceIn stagger-${index + 1}` : 'opacity-0 translate-y-10'
                }`}
              >
                <div className="scan-line"></div>
                <CardHeader className="p-0 relative overflow-hidden">
                  <div className="aspect-video bg-gradient-to-br from-primary/20 to-secondary/20 rounded-t-xl flex items-center justify-center relative">
                    <span className="cyber-mono-alt text-primary animate-hologram-flicker">HOLOGRAPHIC DISPLAY</span>
                    {/* Animated overlay */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000"></div>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <CardTitle className="mb-2 cyber-subheading-alt text-cyber-bright hover-glow transition-all duration-300">
                    <ScanningText text={project.title} />
                  </CardTitle>
                  <CardDescription className="cyber-body-alt text-cyber-muted mb-4">
                    {project.description}
                  </CardDescription>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.map((tech, techIndex) => (
                      <Badge key={techIndex} className="tech-tag hover-neon transition-all duration-300">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                  <Button asChild variant="outline" size="sm" className="cyber-button w-full hover-tilt">
                    <Link href={project.link}>
                      <ExternalLink className="mr-2 h-4 w-4" />
                      ACCESS SYSTEM
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button asChild variant="outline" size="lg" className="cyber-button">
              <Link href="/projects">
                <ArrowRight className="mr-2 h-5 w-5" />
                VIEW ALL SYSTEMS
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Enhanced Skills Overview */}
      <section ref={skillsRef} className="py-20 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center mb-16 transition-all duration-1000 ${skillsVisible ? 'animate-fadeInUp' : 'opacity-0 translate-y-10'}`}>
            <MatrixText
              text="NEURAL CAPABILITIES"
              className="tech-tag inline-block mb-4"
            />
            <h2 className="text-3xl lg:text-4xl font-bold cyber-heading-alt gradient-text mb-4">
              <AnimatedText
                text="CYBER SKILLS"
                animation="glitch"
              />
            </h2>
            <p className="cyber-body-alt text-cyber-muted max-w-2xl mx-auto">
              <AnimatedText
                text="My technical arsenal and digital proficiencies across the full development spectrum."
                animation="slide-up-words"
                delay={500}
              />
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {skills.map((skill, index) => (
              <Card
                key={index}
                className={`glass-card text-center hover-lift interactive-card transition-all duration-700 ${
                  skillsVisible ? `animate-rotateIn stagger-${index + 1}` : 'opacity-0 scale-75'
                }`}
              >
                <div className="scan-line"></div>
                <CardContent className="p-6 relative">
                  <skill.icon className="h-12 w-12 mx-auto mb-4 text-cyber-secondary animate-glow hover-tilt transition-all duration-300" />
                  <h3 className="cyber-subheading-alt text-cyber-bright mb-2">
                    <ScanningText text={skill.name} scanSpeed={3000} />
                  </h3>
                  <div className="w-full bg-muted rounded-full h-3 mb-2 relative overflow-hidden">
                    <div
                      className={`bg-gradient-to-r from-primary via-secondary to-primary h-3 rounded-full transition-all duration-2000 ease-out ${
                        skillsVisible ? 'progress-animate' : 'w-0'
                      }`}
                      style={{
                        width: skillsVisible ? `${skill.level}%` : '0%',
                        animationDelay: `${index * 200}ms`
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-scan"></div>
                    </div>
                  </div>
                  <p className="cyber-mono-alt text-cyber-secondary animate-neon-pulse">
                    <AnimatedText
                      text={`${skill.level}% SYNC`}
                      animation="typewriter"
                      delay={1000 + index * 200}
                      speed={100}
                    />
                  </p>
                  {/* Holographic overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-transparent via-primary/5 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500 rounded-lg"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 relative z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Card className="glass-card">
            <div className="scan-line"></div>
            <CardContent className="p-8">
              <span className="tech-tag inline-block mb-4">INITIALIZE PROTOCOL</span>
              <h2 className="text-3xl lg:text-4xl font-bold cyber-heading gradient-text mb-4">
                READY TO CONNECT?
              </h2>
              <p className="cyber-body text-secondary mb-8 max-w-2xl mx-auto">
                Let's synchronize our neural networks and create something extraordinary in the digital realm.
              </p>
              <Button asChild size="lg" className="cyber-button text-lg">
                <Link href="/contact">
                  <Zap className="mr-2 h-5 w-5" />
                  ESTABLISH CONNECTION
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}