import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Code, Palette, Users, Target, Award, Heart } from "lucide-react";

export default function About() {
  const timeline = [
    {
      year: "2020 - Present",
      title: "Senior Full-Stack Developer",
      company: "Tech Company",
      description: "Leading development of web applications and mentoring junior developers."
    },
    {
      year: "2018 - 2020",
      title: "Frontend Developer",
      company: "Digital Agency",
      description: "Developed responsive web applications and improved user experiences."
    },
    {
      year: "2016 - 2018",
      title: "Junior Developer",
      company: "Startup Inc",
      description: "Started career building web applications and learning modern technologies."
    }
  ];

  const values = [
    {
      icon: Code,
      title: "Clean Code",
      description: "Writing maintainable, efficient, and well-documented code."
    },
    {
      icon: Users,
      title: "User-Centric",
      description: "Putting users first in every design and development decision."
    },
    {
      icon: Target,
      title: "Problem Solving",
      description: "Finding innovative solutions to complex technical challenges."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "Delivering high-quality work that exceeds expectations."
    },
    {
      icon: Heart,
      title: "Passion",
      description: "Bringing enthusiasm and dedication to every project."
    },
    {
      icon: Palette,
      title: "Creativity",
      description: "Combining technical expertise with creative design thinking."
    }
  ];

  return (
    <div className="min-h-screen py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            About Me
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Get to know me better - my journey, my values, and what drives me as a developer and designer.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-20">
          {/* Personal Introduction */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">My Story</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  Hello! I'm [Your Name], a passionate full-stack developer and UX/UI designer with over [X] years of experience creating digital experiences that make a difference.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  My journey in tech began when I discovered my love for both coding and design. I realized that the most impactful digital products are those that seamlessly blend technical excellence with intuitive user experiences.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  Throughout my career, I've had the privilege of working with diverse teams and clients, from startups to established companies. Each project has taught me something new and helped me grow both technically and creatively.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  When I'm not coding or designing, you can find me exploring new technologies, contributing to open-source projects, or sharing my knowledge through technical writing and mentoring.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Facts */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Quick Facts</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Location</h4>
                  <p className="text-muted-foreground">[Your City, Country]</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Experience</h4>
                  <p className="text-muted-foreground">[X]+ Years</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Education</h4>
                  <p className="text-muted-foreground">[Your Degree]</p>
                  <p className="text-muted-foreground">[Your University]</p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Languages</h4>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary">English</Badge>
                    <Badge variant="secondary">[Your Language]</Badge>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Interests</h4>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary">Open Source</Badge>
                    <Badge variant="secondary">UI/UX Design</Badge>
                    <Badge variant="secondary">Photography</Badge>
                    <Badge variant="secondary">Travel</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Professional Journey */}
        <section className="mb-20">
          <h2 className="text-3xl font-bold text-foreground mb-8 text-center">Professional Journey</h2>
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-border"></div>
            
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <div key={index} className={`relative flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className="w-1/2 pr-8">
                    <Card className="ml-auto max-w-md">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle>{item.title}</CardTitle>
                          <Badge variant="outline">{item.year}</Badge>
                        </div>
                        <CardDescription>{item.company}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground">{item.description}</p>
                      </CardContent>
                    </Card>
                  </div>
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-background"></div>
                  <div className="w-1/2 pl-8"></div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Values & Philosophy */}
        <section className="mb-20">
          <h2 className="text-3xl font-bold text-foreground mb-8 text-center">My Values & Philosophy</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <value.icon className="h-12 w-12 mx-auto mb-4 text-primary" />
                  <h3 className="text-lg font-semibold mb-2">{value.title}</h3>
                  <p className="text-muted-foreground">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Personal Interests */}
        <section>
          <h2 className="text-3xl font-bold text-foreground mb-8 text-center">Beyond the Keyboard</h2>
          <Card>
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4">Hobbies & Interests</h3>
                  <ul className="space-y-2 text-muted-foreground">
                    <li>• Photography and visual storytelling</li>
                    <li>• Hiking and outdoor adventures</li>
                    <li>• Reading tech blogs and sci-fi novels</li>
                    <li>• Playing musical instruments</li>
                    <li>• Cooking and experimenting with recipes</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Community Involvement</h3>
                  <ul className="space-y-2 text-muted-foreground">
                    <li>• Active contributor to open-source projects</li>
                    <li>• Mentor for aspiring developers</li>
                    <li>• Speaker at local tech meetups</li>
                    <li>• Volunteer for coding bootcamps</li>
                    <li>• Organizer of design workshops</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}