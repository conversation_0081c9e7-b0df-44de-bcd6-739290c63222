import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Code, 
  Palette, 
  Smartphone, 
  Globe, 
  Zap, 
  Users, 
  Target, 
  Settings,
  CheckCircle,
  Clock,
  DollarSign
} from "lucide-react";

export default function Services() {
  const services = [
    {
      id: 1,
      title: "Full-Stack Web Development",
      icon: Code,
      description: "End-to-end web application development using modern technologies and best practices.",
      features: [
        "Custom web applications",
        "RESTful APIs development",
        "Database design & optimization",
        "Authentication & security",
        "Performance optimization",
        "Scalable architecture"
      ],
      technologies: ["React", "Next.js", "Node.js", "MongoDB", "PostgreSQL", "AWS"],
      timeline: "4-12 weeks",
      price: "Starting from $3,000",
      popular: true
    },
    {
      id: 2,
      title: "UI/UX Design",
      icon: Palette,
      description: "Creating intuitive, beautiful, and user-centered digital experiences.",
      features: [
        "User research & analysis",
        "Wireframing & prototyping",
        "Visual design & branding",
        "Design systems creation",
        "Usability testing",
        "Responsive design"
      ],
      technologies: ["Figma", "Adobe XD", "Sketch", "Principle", "InVision", "Framer"],
      timeline: "2-8 weeks",
      price: "Starting from $1,500",
      popular: false
    },
    {
      id: 3,
      title: "Mobile App Development",
      icon: Smartphone,
      description: "Cross-platform mobile applications with native performance and modern UI.",
      features: [
        "iOS & Android apps",
        "Cross-platform development",
        "App store optimization",
        "Push notifications",
        "Offline functionality",
        "API integration"
      ],
      technologies: ["React Native", "Flutter", "Expo", "Firebase", "Redux", "TypeScript"],
      timeline: "6-16 weeks",
      price: "Starting from $5,000",
      popular: false
    },
    {
      id: 4,
      title: "E-Commerce Solutions",
      icon: Globe,
      description: "Complete e-commerce platforms with payment processing and inventory management.",
      features: [
        "Online store development",
        "Payment gateway integration",
        "Inventory management",
        "Shopping cart optimization",
        "Order management system",
        "SEO optimization"
      ],
      technologies: ["Shopify", "WooCommerce", "Stripe", "PayPal", "React", "Node.js"],
      timeline: "4-10 weeks",
      price: "Starting from $4,000",
      popular: true
    },
    {
      id: 5,
      title: "Performance Optimization",
      icon: Zap,
      description: "Improving website speed, user experience, and search engine rankings.",
      features: [
        "Speed optimization",
        "Code optimization",
        "Image optimization",
        "Caching strategies",
        "SEO improvements",
        "Performance monitoring"
      ],
      technologies: ["Lighthouse", "Web Vitals", "CDN", "Redis", "Webpack", "Vite"],
      timeline: "1-3 weeks",
      price: "Starting from $800",
      popular: false
    },
    {
      id: 6,
      title: "Consulting & Strategy",
      icon: Users,
      description: "Technical guidance and strategic planning for your digital projects.",
      features: [
        "Technical architecture planning",
        "Technology stack selection",
        "Project roadmap creation",
        "Team training & mentoring",
        "Code review & optimization",
        "Best practices implementation"
      ],
      technologies: ["Strategy", "Architecture", "Mentoring", "Planning", "Analysis"],
      timeline: "1-4 weeks",
      price: "Starting from $1,000",
      popular: false
    }
  ];

  const process = [
    {
      step: 1,
      title: "Discovery & Planning",
      description: "Understanding your requirements, goals, and target audience to create a detailed project plan.",
      icon: Target
    },
    {
      step: 2,
      title: "Design & Prototyping",
      description: "Creating wireframes, mockups, and interactive prototypes to visualize the final product.",
      icon: Palette
    },
    {
      step: 3,
      title: "Development",
      description: "Building your project using modern technologies and following best practices.",
      icon: Code
    },
    {
      step: 4,
      title: "Testing & QA",
      description: "Comprehensive testing to ensure quality, performance, and user experience.",
      icon: Settings
    },
    {
      step: 5,
      title: "Deployment & Launch",
      description: "Deploying your project to production and ensuring smooth launch.",
      icon: Globe
    },
    {
      step: 6,
      title: "Support & Maintenance",
      description: "Ongoing support, updates, and maintenance to keep your project running smoothly.",
      icon: Users
    }
  ];

  const packages = [
    {
      name: "Starter",
      price: "$1,500",
      period: "per project",
      description: "Perfect for small projects and startups",
      features: [
        "Up to 10 pages",
        "Basic design",
        "Mobile responsive",
        "Contact form",
        "Basic SEO",
        "1 month support"
      ],
      excluded: ["Custom animations", "Advanced features", "API integration", "E-commerce functionality"],
      popular: false
    },
    {
      name: "Professional",
      price: "$3,500",
      period: "per project",
      description: "Ideal for growing businesses",
      features: [
        "Up to 25 pages",
        "Custom design",
        "Mobile responsive",
        "Advanced forms",
        "SEO optimization",
        "API integration",
        "3 months support",
        "Performance optimization"
      ],
      excluded: ["E-commerce functionality", "Advanced analytics"],
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "quote",
      description: "For large-scale projects",
      features: [
        "Unlimited pages",
        "Premium design",
        "Mobile responsive",
        "Advanced features",
        "Complete SEO",
        "API integration",
        "E-commerce ready",
        "6 months support",
        "Priority support",
        "Custom development"
      ],
      excluded: [],
      popular: false
    }
  ];

  return (
    <div className="min-h-screen py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            My Services
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Comprehensive web development and design services tailored to bring your digital vision to life with quality and innovation.
          </p>
        </div>

        {/* Services Grid */}
        <section className="mb-20">
          <h2 className="text-3xl font-bold text-foreground mb-8 text-center">What I Offer</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <Card key={service.id} className={`relative hover:shadow-lg transition-all duration-300 hover:-translate-y-1 ${service.popular ? 'ring-2 ring-primary' : ''}`}>
                {service.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground">Most Popular</Badge>
                  </div>
                )}
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <service.icon className="h-8 w-8 text-primary" />
                    <CardTitle className="text-xl">{service.title}</CardTitle>
                  </div>
                  <CardDescription>{service.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Key Features:</h4>
                    <ul className="space-y-1">
                      {service.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Technologies:</h4>
                    <div className="flex flex-wrap gap-1">
                      {service.technologies.map((tech, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2 border-t">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      {service.timeline}
                    </div>
                    <div className="flex items-center gap-2 text-sm font-medium">
                      <DollarSign className="h-4 w-4" />
                      {service.price}
                    </div>
                  </div>

                  <Button className="w-full mt-4">
                    Get Quote
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Process Section */}
        <section className="mb-20">
          <h2 className="text-3xl font-bold text-foreground mb-8 text-center">My Process</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {process.map((step) => (
              <Card key={step.step} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <step.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="text-2xl font-bold text-primary mb-2">Step {step.step}</div>
                  <h3 className="text-lg font-semibold mb-2">{step.title}</h3>
                  <p className="text-muted-foreground">{step.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Pricing Packages */}
        <section className="mb-20">
          <h2 className="text-3xl font-bold text-foreground mb-8 text-center">Service Packages</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative ${pkg.popular ? 'ring-2 ring-primary' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-primary text-primary-foreground">Recommended</Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-primary">
                    {pkg.price}
                    <span className="text-sm font-normal text-muted-foreground">/{pkg.period}</span>
                  </div>
                  <CardDescription>{pkg.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Included:</h4>
                    <ul className="space-y-1">
                      {pkg.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  {pkg.excluded.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-2">Not Included:</h4>
                      <ul className="space-y-1">
                        {pkg.excluded.map((feature, index) => (
                          <li key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                            <div className="h-4 w-4 rounded-full border border-muted-foreground"></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-primary' : 'variant-outline'}`}
                    variant={pkg.popular ? 'default' : 'outline'}
                  >
                    {pkg.price === 'Custom' ? 'Contact for Quote' : 'Get Started'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center">
          <Card className="max-w-4xl mx-auto">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold text-foreground mb-4">
                Ready to Start Your Project?
              </h2>
              <p className="text-lg text-muted-foreground mb-6">
                Let's discuss your requirements and create a solution that exceeds your expectations.
              </p>
              <Button size="lg" className="text-lg">
                Get in Touch
              </Button>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}