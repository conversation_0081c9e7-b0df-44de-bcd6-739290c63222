@import "tailwindcss";
@import "tw-animate-css";
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&family=Exo+2:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500;600;700&display=swap');

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--cyber-black);
  --color-foreground: var(--text-primary);
  --font-sans: var(--font-rajdhani);
  --font-mono: var(--font-jetbrains);
  --color-sidebar-ring: var(--neon-blue);
  --color-sidebar-border: var(--glass-border);
  --color-sidebar-accent-foreground: var(--text-primary);
  --color-sidebar-accent: var(--glass-bg);
  --color-sidebar-primary-foreground: var(--text-primary);
  --color-sidebar-primary: var(--glass-bg);
  --color-sidebar-foreground: var(--text-primary);
  --color-sidebar: var(--midnight);
  --color-chart-5: var(--neon-pink);
  --color-chart-4: var(--neon-purple);
  --color-chart-3: var(--neon-blue);
  --color-chart-2: var(--neon-green);
  --color-chart-1: var(--neon-blue);
  --color-ring: var(--neon-blue);
  --color-input: var(--glass-bg);
  --color-border: var(--glass-border);
  --color-destructive: var(--neon-pink);
  --color-accent-foreground: var(--text-primary);
  --color-accent: var(--glass-bg);
  --color-muted-foreground: var(--text-tertiary);
  --color-muted: var(--void);
  --color-secondary-foreground: var(--text-secondary);
  --color-secondary: var(--glass-bg);
  --color-primary-foreground: var(--cyber-black);
  --color-primary: var(--neon-blue);
  --color-popover-foreground: var(--text-primary);
  --color-popover: var(--glass-bg);
  --color-card-foreground: var(--text-primary);
  --color-card: var(--glass-bg);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.875rem;
  
  /* Cyberpunk Color Palette */
  --cyber-black: #0A0A0F;
  --midnight: #12121A;
  --void: #1A1A2E;
  --neon-blue: #00D4FF;
  --neon-purple: #B967FF;
  --neon-pink: #FF006E;
  --neon-green: #39FF14;
  
  /* Gradient Combinations */
  --gradient-primary: linear-gradient(135deg, #00D4FF 0%, #B967FF 100%);
  --gradient-secondary: linear-gradient(135deg, #FF006E 0%, #B967FF 100%);
  --gradient-tertiary: linear-gradient(135deg, #39FF14 0%, #00D4FF 100%);
  
  /* Enhanced UI Elements */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --text-primary: #FFFFFF;
  --text-secondary: rgba(0, 212, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-accent: #00D4FF;
  --text-highlight: #B967FF;
  
  /* Interactive States */
  --hover-glow: rgba(0, 212, 255, 0.3);
  --active-glow: rgba(185, 103, 255, 0.4);
  --danger-glow: rgba(255, 0, 110, 0.3);
  
  /* Enhanced Typography */
  --font-orbitron: 'Orbitron', sans-serif;
  --font-rajdhani: 'Rajdhani', sans-serif;
  --font-jetbrains: 'JetBrains Mono', monospace;
  --font-exo: 'Exo 2', sans-serif;
  --font-space: 'Space Grotesk', sans-serif;
  --font-fira: 'Fira Code', monospace;
  
  /* Background */
  --background: oklch(0.05 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.08 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.08 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.6 0.3 220);
  --primary-foreground: oklch(0.05 0 0);
  --secondary: oklch(0.1 0 0);
  --secondary-foreground: oklch(0.9 0 0);
  --muted: oklch(0.08 0 0);
  --muted-foreground: oklch(0.6 0 0);
  --accent: oklch(0.1 0 0);
  --accent-foreground: oklch(0.9 0 0);
  --destructive: oklch(0.6 0.3 350);
  --border: oklch(0.2 0 0);
  --input: oklch(0.1 0 0);
  --ring: oklch(0.6 0.3 220);
  --chart-1: oklch(0.6 0.3 220);
  --chart-2: oklch(0.5 0.3 280);
  --chart-3: oklch(0.4 0.3 320);
  --chart-4: oklch(0.6 0.3 350);
  --chart-5: oklch(0.5 0.3 140);
  --sidebar: oklch(0.08 0 0);
  --sidebar-foreground: oklch(0.9 0 0);
  --sidebar-primary: oklch(0.6 0.3 220);
  --sidebar-primary-foreground: oklch(0.05 0 0);
  --sidebar-accent: oklch(0.1 0 0);
  --sidebar-accent-foreground: oklch(0.9 0 0);
  --sidebar-border: oklch(0.2 0 0);
  --sidebar-ring: oklch(0.6 0.3 220);
}

.dark {
  --background: oklch(0.05 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.08 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.08 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.6 0.3 220);
  --primary-foreground: oklch(0.05 0 0);
  --secondary: oklch(0.1 0 0);
  --secondary-foreground: oklch(0.9 0 0);
  --muted: oklch(0.08 0 0);
  --muted-foreground: oklch(0.6 0 0);
  --accent: oklch(0.1 0 0);
  --accent-foreground: oklch(0.9 0 0);
  --destructive: oklch(0.6 0.3 350);
  --border: oklch(0.2 0 0);
  --input: oklch(0.1 0 0);
  --ring: oklch(0.6 0.3 220);
  --chart-1: oklch(0.6 0.3 220);
  --chart-2: oklch(0.5 0.3 280);
  --chart-3: oklch(0.4 0.3 320);
  --chart-4: oklch(0.6 0.3 350);
  --chart-5: oklch(0.5 0.3 140);
  --sidebar: oklch(0.08 0 0);
  --sidebar-foreground: oklch(0.9 0 0);
  --sidebar-primary: oklch(0.6 0.3 220);
  --sidebar-primary-foreground: oklch(0.05 0 0);
  --sidebar-accent: oklch(0.1 0 0);
  --sidebar-accent-foreground: oklch(0.9 0 0);
  --sidebar-border: oklch(0.2 0 0);
  --sidebar-ring: oklch(0.6 0.3 220);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-rajdhani);
    background: var(--cyber-black);
    background-image: 
      radial-gradient(ellipse at top, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
      radial-gradient(ellipse at bottom, rgba(185, 103, 255, 0.05) 0%, transparent 50%),
      linear-gradient(135deg, var(--cyber-black) 0%, var(--midnight) 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
  }
  
  /* Animated Grid Background */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: grid-move 20s linear infinite;
    pointer-events: none;
    z-index: -1;
  }
  
  /* Enhanced Custom Animations */
  @keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: scale(0.3) translateY(50px);
    }
    50% {
      opacity: 1;
      transform: scale(1.05) translateY(-10px);
    }
    70% {
      transform: scale(0.95) translateY(5px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes rotateIn {
    from {
      opacity: 0;
      transform: rotate(-180deg) scale(0.5);
    }
    to {
      opacity: 1;
      transform: rotate(0deg) scale(1);
    }
  }

  @keyframes flipIn {
    from {
      opacity: 0;
      transform: perspective(400px) rotateY(90deg);
    }
    to {
      opacity: 1;
      transform: perspective(400px) rotateY(0deg);
    }
  }
  
  @keyframes pulse {
    0%, 100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.5;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.2);
      opacity: 0.8;
    }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 5px var(--neon-blue), 0 0 10px var(--neon-blue), 0 0 15px var(--neon-blue);
    }
    50% {
      box-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
    }
  }

  @keyframes scan {
    0% { top: 0; }
    100% { top: 100%; }
  }

  @keyframes float {
    0% { transform: translateY(100vh) translateX(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(-100vh) translateX(100px); opacity: 0; }
  }

  @keyframes glitch {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
  }

  /* New Advanced Animations */
  @keyframes neonPulse {
    0%, 100% {
      text-shadow:
        0 0 5px var(--neon-blue),
        0 0 10px var(--neon-blue),
        0 0 15px var(--neon-blue),
        0 0 20px var(--neon-blue);
    }
    50% {
      text-shadow:
        0 0 2px var(--neon-blue),
        0 0 5px var(--neon-blue),
        0 0 8px var(--neon-blue),
        0 0 12px var(--neon-blue);
    }
  }

  @keyframes dataStream {
    0% { transform: translateY(-100%); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(100vh); opacity: 0; }
  }

  @keyframes hologramFlicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
    51% { opacity: 0.9; }
    52% { opacity: 0.7; }
    53% { opacity: 1; }
  }

  @keyframes matrixRain {
    0% { transform: translateY(-100vh); }
    100% { transform: translateY(100vh); }
  }

  @keyframes circuitTrace {
    0% { stroke-dashoffset: 1000; }
    100% { stroke-dashoffset: 0; }
  }

  @keyframes energyWave {
    0% {
      transform: scale(0.8) rotate(0deg);
      opacity: 1;
    }
    100% {
      transform: scale(1.2) rotate(360deg);
      opacity: 0;
    }
  }
  
  /* Enhanced Animation Classes */
  .animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-fadeInDown {
    animation: fadeInDown 0.6s ease-out forwards;
  }

  .animate-fadeIn {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.6s ease-out forwards;
  }

  .animate-slideInRight {
    animation: slideInRight 0.6s ease-out forwards;
  }

  .animate-scaleIn {
    animation: scaleIn 0.5s ease-out forwards;
  }

  .animate-bounceIn {
    animation: bounceIn 0.8s ease-out forwards;
  }

  .animate-rotateIn {
    animation: rotateIn 0.6s ease-out forwards;
  }

  .animate-flipIn {
    animation: flipIn 0.6s ease-out forwards;
  }

  .animate-pulse-glow {
    animation: pulse 4s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-neon-pulse {
    animation: neonPulse 2s ease-in-out infinite;
  }

  .animate-scan {
    animation: scan 3s linear infinite;
  }

  .animate-float {
    animation: float 10s infinite linear;
  }

  .animate-glitch {
    animation: glitch 0.3s ease-in-out infinite;
  }

  .animate-data-stream {
    animation: dataStream 3s linear infinite;
  }

  .animate-hologram-flicker {
    animation: hologramFlicker 4s ease-in-out infinite;
  }

  .animate-matrix-rain {
    animation: matrixRain 8s linear infinite;
  }

  .animate-energy-wave {
    animation: energyWave 2s ease-out infinite;
  }
  
  /* Enhanced Stagger Animations */
  .stagger-1 { animation-delay: 0.1s; }
  .stagger-2 { animation-delay: 0.2s; }
  .stagger-3 { animation-delay: 0.3s; }
  .stagger-4 { animation-delay: 0.4s; }
  .stagger-5 { animation-delay: 0.5s; }
  .stagger-6 { animation-delay: 0.6s; }
  .stagger-7 { animation-delay: 0.7s; }
  .stagger-8 { animation-delay: 0.8s; }

  /* Enhanced Hover Effects */
  .hover-lift {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
  }

  .hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 212, 255, 0.15),
      0 0 20px rgba(0, 212, 255, 0.1);
  }

  .hover-lift:hover::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
    border-radius: inherit;
    z-index: -1;
    opacity: 0.3;
    filter: blur(8px);
  }

  .hover-glow {
    transition: all 0.3s ease;
    position: relative;
  }

  .hover-glow:hover {
    color: var(--neon-blue);
    text-shadow:
      0 0 5px var(--neon-blue),
      0 0 10px var(--neon-blue),
      0 0 15px var(--neon-blue),
      0 0 20px var(--neon-blue);
    transform: scale(1.05);
  }

  .hover-tilt {
    transition: transform 0.3s ease;
  }

  .hover-tilt:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg) scale(1.02);
  }

  .hover-neon {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .hover-neon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
  }

  .hover-neon:hover::before {
    left: 100%;
  }

  .hover-neon:hover {
    box-shadow:
      0 0 20px var(--neon-blue),
      inset 0 0 20px rgba(0, 212, 255, 0.1);
    border-color: var(--neon-blue);
  }
  
  /* Progress bar animation */
  .progress-animate {
    transition: width 1.5s ease-out;
  }
  
  /* Gradient text */
  .gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .gradient-text-secondary {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .gradient-text-tertiary {
    background: var(--gradient-tertiary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Glass morphism effect */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
  }
  
  .glass-card {
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(18, 18, 26, 0.8));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.5s;
  }

  .glass-card::after {
    content: '';
    position: absolute;
    inset: -1px;
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink), var(--neon-green));
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    filter: blur(2px);
  }

  .glass-card:hover::before {
    left: 100%;
  }

  .glass-card:hover::after {
    opacity: 0.3;
  }

  .glass-card:hover {
    border-color: rgba(0, 212, 255, 0.6);
    box-shadow:
      0 8px 32px rgba(0, 212, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
  
  /* Enhanced Cyberpunk Buttons */
  .cyber-button {
    background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    border: 1px solid rgba(0, 212, 255, 0.5);
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 600;
    font-family: var(--font-rajdhani);
  }

  .cyber-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .cyber-button::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .cyber-button:hover::before {
    left: 100%;
  }

  .cyber-button:hover::after {
    opacity: 0.1;
  }

  .cyber-button:hover {
    border-color: var(--neon-blue);
    box-shadow:
      0 0 30px rgba(0, 212, 255, 0.6),
      0 5px 15px rgba(0, 212, 255, 0.3);
    transform: translateY(-3px) scale(1.02);
    text-shadow: 0 0 10px var(--neon-blue);
  }

  .cyber-button:active {
    transform: translateY(-1px) scale(0.98);
    box-shadow:
      0 0 20px rgba(0, 212, 255, 0.8),
      0 2px 8px rgba(0, 212, 255, 0.4);
  }
  
  /* Scan line effect */
  .scan-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--neon-blue), transparent);
    animation: scan 3s linear infinite;
  }
  
  /* Enhanced Tech tags */
  .tech-tag {
    background: rgba(0, 212, 255, 0.15);
    border: 1px solid rgba(0, 212, 255, 0.4);
    color: #FFFFFF;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-family: var(--font-jetbrains);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
  }

  .tech-tag:hover {
    background: rgba(0, 212, 255, 0.25);
    border-color: var(--neon-blue);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
    color: #FFFFFF;
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
  }
  
  /* Enhanced Typography Classes */
  .cyber-heading {
    font-family: var(--font-orbitron);
    font-weight: 700;
    letter-spacing: -0.02em;
    text-transform: uppercase;
    position: relative;
  }

  .cyber-heading-alt {
    font-family: var(--font-exo);
    font-weight: 800;
    letter-spacing: 0.02em;
    text-transform: uppercase;
  }

  .cyber-subheading {
    font-family: var(--font-rajdhani);
    font-weight: 600;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }

  .cyber-subheading-alt {
    font-family: var(--font-space);
    font-weight: 600;
    letter-spacing: 0.03em;
    text-transform: uppercase;
  }

  .cyber-body {
    font-family: var(--font-rajdhani);
    font-weight: 300;
    line-height: 1.6;
  }

  .cyber-body-alt {
    font-family: var(--font-space);
    font-weight: 400;
    line-height: 1.7;
  }

  .cyber-mono {
    font-family: var(--font-jetbrains);
    letter-spacing: 0.05em;
  }

  .cyber-mono-alt {
    font-family: var(--font-fira);
    letter-spacing: 0.03em;
  }

  /* Animated Typography Effects */
  .text-typewriter {
    overflow: hidden;
    border-right: 2px solid var(--neon-blue);
    white-space: nowrap;
    animation: typewriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
  }

  @keyframes typewriter {
    from { width: 0; }
    to { width: 100%; }
  }

  @keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--neon-blue); }
  }

  .text-glitch {
    position: relative;
    color: var(--text-primary);
    animation: glitch-text 2s infinite;
  }

  .text-glitch::before,
  .text-glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .text-glitch::before {
    animation: glitch-text-1 0.5s infinite;
    color: var(--neon-pink);
    z-index: -1;
  }

  .text-glitch::after {
    animation: glitch-text-2 0.5s infinite;
    color: var(--neon-blue);
    z-index: -2;
  }

  @keyframes glitch-text {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
  }

  @keyframes glitch-text-1 {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(2px, -2px); }
    40% { transform: translate(-2px, 2px); }
    60% { transform: translate(-2px, -2px); }
    80% { transform: translate(2px, 2px); }
  }

  @keyframes glitch-text-2 {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, -2px); }
    40% { transform: translate(2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(-2px, 2px); }
  }

  .text-neon-flicker {
    animation: neon-flicker 1.5s infinite alternate;
  }

  @keyframes neon-flicker {
    0%, 18%, 22%, 25%, 53%, 57%, 100% {
      text-shadow:
        0 0 4px var(--neon-blue),
        0 0 11px var(--neon-blue),
        0 0 19px var(--neon-blue),
        0 0 40px var(--neon-blue);
    }
    20%, 24%, 55% {
      text-shadow: none;
    }
  }

  .text-hologram {
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: hologram-shift 3s ease-in-out infinite;
  }

  @keyframes hologram-shift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(180deg); }
  }
  
  /* Hero glow effect */
  .hero-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.2) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    animation: pulse 4s ease-in-out infinite;
    pointer-events: none;
  }
  
  /* Enhanced Navigation styles */
  .cyber-nav {
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 212, 255, 0.4);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 212, 255, 0.1);
  }

  .cyber-nav-link {
    position: relative;
    padding: 0.5rem 1rem;
    color: rgba(255, 255, 255, 0.85);
    transition: all 0.3s ease;
    font-family: var(--font-rajdhani);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  .cyber-nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  .cyber-nav-link:hover::after {
    width: 80%;
    box-shadow: 0 0 10px var(--neon-blue);
  }

  .cyber-nav-link:hover {
    color: #FFFFFF;
    text-shadow: 0 0 8px var(--neon-blue);
  }

  .cyber-nav-link.active {
    color: var(--neon-blue);
    text-shadow: 0 0 8px var(--neon-blue);
  }
  
  /* Particle effect */
  .particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--neon-blue);
    border-radius: 50%;
    animation: float 10s infinite linear;
    pointer-events: none;
  }

  /* Enhanced Modern Utility Classes */
  .text-glow {
    text-shadow:
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 30px currentColor;
  }

  .border-glow {
    box-shadow:
      0 0 10px currentColor,
      inset 0 0 10px rgba(255, 255, 255, 0.1);
  }

  /* Enhanced Text Color Classes */
  .text-cyber-primary {
    color: #FFFFFF;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }

  .text-cyber-secondary {
    color: var(--neon-blue);
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
  }

  .text-cyber-accent {
    color: var(--neon-purple);
    text-shadow: 0 0 5px rgba(185, 103, 255, 0.3);
  }

  .text-cyber-highlight {
    color: var(--neon-green);
    text-shadow: 0 0 5px rgba(57, 255, 20, 0.3);
  }

  .text-cyber-muted {
    color: rgba(255, 255, 255, 0.6);
  }

  .text-cyber-bright {
    color: #FFFFFF;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
  }

  .backdrop-blur-strong {
    backdrop-filter: blur(40px) saturate(180%);
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-3d {
    transform-style: preserve-3d;
  }

  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Interactive Elements */
  .interactive-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .interactive-card:hover {
    transform: translateY(-4px) rotateX(5deg);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(0, 212, 255, 0.2);
  }

  /* Loading States */
  .loading-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Scroll Animations */
  .scroll-fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
  }

  .scroll-fade-in.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .scroll-slide-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease-out;
  }

  .scroll-slide-left.visible {
    opacity: 1;
    transform: translateX(0);
  }

  .scroll-slide-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease-out;
  }

  .scroll-slide-right.visible {
    opacity: 1;
    transform: translateX(0);
  }

  /* Neon Color Utilities */
  .text-neon-blue {
    color: var(--neon-blue);
  }

  .text-neon-purple {
    color: var(--neon-purple);
  }

  .text-neon-pink {
    color: var(--neon-pink);
  }

  .text-neon-green {
    color: var(--neon-green);
  }

  .bg-neon-blue\/10 {
    background-color: rgba(0, 212, 255, 0.1);
  }

  .bg-neon-purple\/10 {
    background-color: rgba(185, 103, 255, 0.1);
  }

  .bg-neon-pink\/10 {
    background-color: rgba(255, 0, 110, 0.1);
  }

  .bg-neon-green\/10 {
    background-color: rgba(57, 255, 20, 0.1);
  }
}
