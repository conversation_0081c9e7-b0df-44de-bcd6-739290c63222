"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { <PERSON>u, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const navigation = [
  { name: "Home", href: "/" },
  { name: "About", href: "/about" },
  { name: "Projects", href: "/projects" },
  { name: "Skills", href: "/skills" },
  { name: "Services", href: "/services" },
  { name: "Contact", href: "/contact" },
];

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  return (
    <nav className="cyber-nav">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Enhanced Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center group relative">
              <span className="text-2xl font-bold cyber-heading-alt text-cyber-bright transition-all duration-300 group-hover:text-glow">
                CYBER
              </span>
              <span className="text-2xl font-bold cyber-heading-alt text-cyber-secondary ml-1 transition-all duration-300 group-hover:animate-neon-pulse">
                PORT
              </span>
              {/* Animated underline */}
              <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary group-hover:w-full transition-all duration-500"></div>
            </Link>
          </div>

          {/* Enhanced Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-1">
              {navigation.map((item, index) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`cyber-nav-link relative group transition-all duration-300 ${
                    pathname === item.href
                      ? "text-cyber-secondary animate-neon-pulse active"
                      : "text-cyber-primary hover:text-cyber-bright"
                  }`}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <span className="relative z-10">{item.name}</span>
                  {/* Enhanced hover effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-md opacity-0 group-hover:opacity-100 transition-all duration-300 -z-10"></div>
                  {/* Animated border */}
                  <div className="absolute bottom-0 left-1/2 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary group-hover:w-full group-hover:left-0 transition-all duration-500"></div>
                  {/* Glow effect */}
                  <div className="absolute inset-0 rounded-md opacity-0 group-hover:opacity-30 transition-all duration-300 border-glow -z-20"></div>
                </Link>
              ))}
            </div>
          </div>

          {/* Enhanced Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(!isOpen)}
              className="text-cyber-primary hover:text-cyber-bright transition-all duration-300 hover-neon relative group"
            >
              <div className={`transition-all duration-300 ${isOpen ? 'rotate-180' : 'rotate-0'}`}>
                {isOpen ? (
                  <X className="h-6 w-6 animate-rotateIn" />
                ) : (
                  <Menu className="h-6 w-6 group-hover:animate-pulse" />
                )}
              </div>
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-md opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden glass-card border-t border-t-cyan-500/20 animate-fadeInDown backdrop-blur-strong">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navigation.map((item, index) => (
              <Link
                key={item.name}
                href={item.href}
                className={`block px-3 py-2 rounded-md text-base font-medium cyber-nav-link relative group transition-all duration-300 animate-slideInLeft ${
                  pathname === item.href
                    ? "text-cyber-secondary bg-primary/10 border-glow"
                    : "text-cyber-primary hover:text-cyber-bright hover:bg-primary/5"
                } stagger-${index + 1}`}
                onClick={() => setIsOpen(false)}
              >
                <span className="relative z-10">{item.name}</span>
                {/* Mobile hover effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-md opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                {/* Active indicator */}
                {pathname === item.href && (
                  <div className="absolute left-0 top-1/2 w-1 h-6 bg-gradient-to-b from-primary to-secondary rounded-r-full transform -translate-y-1/2 animate-pulse"></div>
                )}
              </Link>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
}