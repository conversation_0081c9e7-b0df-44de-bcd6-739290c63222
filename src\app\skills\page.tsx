import { <PERSON>, CardContent, Card<PERSON>escription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  Code, 
  Palette, 
  Database, 
  Smartphone, 
  Globe, 
  Server, 
  Zap, 
  Layers,
  Target,
  Lightbulb,
  Users,
  Settings
} from "lucide-react";

export default function Skills() {
  const technicalSkills = [
    {
      category: "Frontend Development",
      icon: Code,
      description: "Creating responsive, interactive user interfaces",
      skills: [
        { name: "React/Next.js", level: 95, years: 4 },
        { name: "TypeScript", level: 90, years: 3 },
        { name: "JavaScript", level: 95, years: 5 },
        { name: "HTML/CSS", level: 95, years: 5 },
        { name: "Tailwind CSS", level: 90, years: 3 },
        { name: "Redux", level: 85, years: 3 },
        { name: "Vue.js", level: 75, years: 2 },
      ]
    },
    {
      category: "Backend Development",
      icon: Server,
      description: "Building robust server-side applications and APIs",
      skills: [
        { name: "Node.js", level: 90, years: 4 },
        { name: "Express.js", level: 85, years: 3 },
        { name: "Python", level: 80, years: 3 },
        { name: "REST APIs", level: 90, years: 4 },
        { name: "GraphQL", level: 75, years: 2 },
        { name: "Authentication", level: 85, years: 3 },
        { name: "Serverless", level: 70, years: 2 },
      ]
    },
    {
      category: "Database & Storage",
      icon: Database,
      description: "Managing data efficiently and securely",
      skills: [
        { name: "MongoDB", level: 85, years: 3 },
        { name: "PostgreSQL", level: 80, years: 3 },
        { name: "MySQL", level: 75, years: 2 },
        { name: "Redis", level: 70, years: 2 },
        { name: "Firebase", level: 85, years: 3 },
        { name: "Prisma ORM", level: 80, years: 2 },
        { name: "AWS S3", level: 75, years: 2 },
      ]
    },
    {
      category: "Mobile Development",
      icon: Smartphone,
      description: "Creating cross-platform mobile applications",
      skills: [
        { name: "React Native", level: 80, years: 3 },
        { name: "Flutter", level: 70, years: 2 },
        { name: "iOS Development", level: 65, years: 2 },
        { name: "Android Development", level: 65, years: 2 },
        { name: "Expo", level: 75, years: 2 },
        { name: "Mobile UI/UX", level: 85, years: 3 },
        { name: "App Performance", level: 75, years: 2 },
      ]
    }
  ];

  const designSkills = [
    {
      category: "UI/UX Design",
      icon: Palette,
      description: "Designing intuitive and beautiful user experiences",
      skills: [
        { name: "Figma", level: 90, years: 4 },
        { name: "Adobe XD", level: 85, years: 3 },
        { name: "Sketch", level: 80, years: 3 },
        { name: "Prototyping", level: 85, years: 3 },
        { name: "User Research", level: 75, years: 2 },
        { name: "Wireframing", level: 90, years: 4 },
        { name: "Design Systems", level: 85, years: 3 },
      ]
    },
    {
      category: "Visual Design",
      icon: Layers,
      description: "Creating visually appealing graphics and layouts",
      skills: [
        { name: "Adobe Photoshop", level: 85, years: 4 },
        { name: "Adobe Illustrator", level: 80, years: 3 },
        { name: "Typography", level: 85, years: 4 },
        { name: "Color Theory", level: 90, years: 4 },
        { name: "Layout Design", level: 85, years: 3 },
        { name: "Icon Design", level: 75, years: 2 },
        { name: "Brand Identity", level: 70, years: 2 },
      ]
    },
    {
      category: "User Experience",
      icon: Users,
      description: "Understanding and improving user interactions",
      skills: [
        { name: "User Testing", level: 80, years: 3 },
        { name: "Usability Analysis", level: 85, years: 3 },
        { name: "Information Architecture", level: 80, years: 3 },
        { name: "Interaction Design", level: 85, years: 3 },
        { name: "Accessibility", level: 75, years: 2 },
        { name: "User Personas", level: 80, years: 3 },
        { name: "Journey Mapping", level: 75, years: 2 },
      ]
    }
  ];

  const toolsAndTechnologies = [
    {
      category: "Development Tools",
      icon: Settings,
      items: [
        "Git & GitHub", "VS Code", "WebStorm", "Chrome DevTools", 
        "Postman", "Docker", "Webpack", "Vite", "ESLint", "Prettier"
      ]
    },
    {
      category: "Design Tools",
      icon: Palette,
      items: [
        "Figma", "Adobe Creative Suite", "Sketch", "InVision", 
        "Framer", "Principle", "Zeplin", "Abstract", "Loom", "Miro"
      ]
    },
    {
      category: "Project Management",
      icon: Target,
      items: [
        "Jira", "Trello", "Asana", "Notion", "Slack", 
        "Discord", "Zoom", "Monday.com", "ClickUp", "Linear"
      ]
    },
    {
      category: "Cloud & DevOps",
      icon: Globe,
      items: [
        "AWS", "Vercel", "Netlify", "Heroku", "Firebase", 
        "GitHub Actions", "Docker", "Kubernetes", "CI/CD", "Monitoring"
      ]
    }
  ];

  const softSkills = [
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Working effectively in cross-functional teams and communicating clearly with stakeholders.",
      level: 95
    },
    {
      icon: Lightbulb,
      title: "Problem Solving",
      description: "Analyzing complex problems and developing innovative, practical solutions.",
      level: 90
    },
    {
      icon: Target,
      title: "Project Management",
      description: "Planning, executing, and delivering projects on time and within budget.",
      level: 85
    },
    {
      icon: Zap,
      title: "Adaptability",
      description: "Quickly learning new technologies and adapting to changing requirements.",
      level: 95
    }
  ];

  const certifications = [
    { name: "AWS Certified Developer", issuer: "Amazon Web Services", year: "2023" },
    { name: "Google UX Design Certificate", issuer: "Google", year: "2022" },
    { name: "React Developer Certification", issuer: "Meta", year: "2022" },
    { name: "MongoDB Certified Developer", issuer: "MongoDB", year: "2021" },
  ];

  return (
    <div className="min-h-screen py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Skills & Expertise
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            A comprehensive overview of my technical capabilities, design skills, and professional expertise across the full development stack.
          </p>
        </div>

        <Tabs defaultValue="technical" className="space-y-8">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="technical">Technical</TabsTrigger>
            <TabsTrigger value="design">Design</TabsTrigger>
            <TabsTrigger value="tools">Tools</TabsTrigger>
            <TabsTrigger value="soft">Soft Skills</TabsTrigger>
          </TabsList>

          {/* Technical Skills */}
          <TabsContent value="technical" className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {technicalSkills.map((category, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <category.icon className="h-8 w-8 text-primary" />
                      <div>
                        <CardTitle>{category.category}</CardTitle>
                        <CardDescription>{category.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {category.skills.map((skill, skillIndex) => (
                      <div key={skillIndex} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{skill.name}</span>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {skill.years} {skill.years === 1 ? 'year' : 'years'}
                            </Badge>
                            <span className="text-sm text-muted-foreground">{skill.level}%</span>
                          </div>
                        </div>
                        <Progress value={skill.level} className="h-2" />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Design Skills */}
          <TabsContent value="design" className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {designSkills.map((category, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <category.icon className="h-8 w-8 text-primary" />
                      <div>
                        <CardTitle>{category.category}</CardTitle>
                        <CardDescription>{category.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {category.skills.map((skill, skillIndex) => (
                      <div key={skillIndex} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{skill.name}</span>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {skill.years} {skill.years === 1 ? 'year' : 'years'}
                            </Badge>
                            <span className="text-sm text-muted-foreground">{skill.level}%</span>
                          </div>
                        </div>
                        <Progress value={skill.level} className="h-2" />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Tools & Technologies */}
          <TabsContent value="tools" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {toolsAndTechnologies.map((category, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <category.icon className="h-6 w-6 text-primary" />
                      <CardTitle className="text-lg">{category.category}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {category.items.map((item, itemIndex) => (
                        <Badge key={itemIndex} variant="secondary" className="text-xs">
                          {item}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Soft Skills */}
          <TabsContent value="soft" className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {softSkills.map((skill, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <skill.icon className="h-8 w-8 text-primary mt-1" />
                      <div className="flex-1 space-y-3">
                        <div>
                          <h3 className="text-lg font-semibold">{skill.title}</h3>
                          <p className="text-muted-foreground">{skill.description}</p>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Proficiency</span>
                            <span className="text-sm text-muted-foreground">{skill.level}%</span>
                          </div>
                          <Progress value={skill.level} className="h-2" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Certifications */}
            <Card>
              <CardHeader>
                <CardTitle>Certifications & Credentials</CardTitle>
                <CardDescription>Professional certifications and ongoing education</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {certifications.map((cert, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h4 className="font-semibold">{cert.name}</h4>
                        <p className="text-sm text-muted-foreground">{cert.issuer}</p>
                      </div>
                      <Badge variant="outline">{cert.year}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}