"use client";

import { useEffect, useState } from 'react';

// Hook to detect user's motion preferences
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}

// Hook to detect device performance capabilities
export function useDeviceCapabilities() {
  const [capabilities, setCapabilities] = useState({
    isLowEnd: false,
    isMobile: false,
    supportsWebGL: false,
    deviceMemory: 4, // Default fallback
    hardwareConcurrency: 4, // Default fallback
  });

  useEffect(() => {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );

    // Check for device memory (if available)
    const deviceMemory = (navigator as any).deviceMemory || 4;
    const hardwareConcurrency = navigator.hardwareConcurrency || 4;

    // Check WebGL support
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    const supportsWebGL = !!gl;

    // Determine if device is low-end based on available metrics
    const isLowEnd = deviceMemory <= 2 || hardwareConcurrency <= 2 || isMobile;

    setCapabilities({
      isLowEnd,
      isMobile,
      supportsWebGL,
      deviceMemory,
      hardwareConcurrency,
    });
  }, []);

  return capabilities;
}

// Hook for adaptive animation settings
export function useAdaptiveAnimations() {
  const prefersReducedMotion = useReducedMotion();
  const { isLowEnd, isMobile } = useDeviceCapabilities();

  const animationSettings = {
    // Disable animations if user prefers reduced motion
    enableAnimations: !prefersReducedMotion,
    
    // Reduce animation complexity on low-end devices
    enableComplexAnimations: !isLowEnd && !prefersReducedMotion,
    
    // Reduce particle count on mobile/low-end devices (overall reduced)
    particleCount: isLowEnd ? 10 : isMobile ? 20 : 30,
    
    // Adjust animation duration based on device capabilities
    animationDuration: isLowEnd ? 0.2 : 0.3,
    
    // Enable/disable expensive effects
    enableParticles: !isLowEnd && !prefersReducedMotion,
    enableBackgroundAnimations: !isLowEnd && !prefersReducedMotion,
    enableHoverEffects: !isMobile && !prefersReducedMotion,
    
    // Frame rate targets
    targetFPS: isLowEnd ? 30 : 60,
  };

  return animationSettings;
}

// Hook for performance monitoring
export function usePerformanceMonitor() {
  const [performanceMetrics, setPerformanceMetrics] = useState({
    fps: 60,
    frameTime: 16.67,
    isPerformanceGood: true,
  });

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measurePerformance = (currentTime: number) => {
      frameCount++;
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        const frameTime = 1000 / fps;
        const isPerformanceGood = fps >= 30;

        setPerformanceMetrics({
          fps,
          frameTime,
          isPerformanceGood,
        });

        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(measurePerformance);
    };

    animationId = requestAnimationFrame(measurePerformance);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return performanceMetrics;
}

// Hook for lazy loading animations
export function useLazyAnimation(threshold = 0.1) {
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const { enableAnimations } = useAdaptiveAnimations();

  useEffect(() => {
    if (!enableAnimations) {
      setShouldAnimate(false);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setShouldAnimate(true);
          observer.disconnect();
        }
      },
      { threshold }
    );

    const element = document.querySelector('[data-lazy-animate]');
    if (element) {
      observer.observe(element);
    }

    return () => observer.disconnect();
  }, [threshold, enableAnimations]);

  return shouldAnimate;
}

// Utility function to create performance-aware CSS classes
export function getPerformanceAwareClasses(baseClasses: string, animationClasses: string) {
  const { enableAnimations, enableComplexAnimations } = useAdaptiveAnimations();
  
  if (!enableAnimations) {
    return baseClasses;
  }
  
  if (!enableComplexAnimations) {
    // Return simplified animation classes
    return `${baseClasses} animate-fadeIn`;
  }
  
  return `${baseClasses} ${animationClasses}`;
}
