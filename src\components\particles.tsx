"use client";

import { useEffect, useRef, useCallback } from "react";

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
  life: number;
  maxLife: number;
  trail: { x: number; y: number; opacity: number }[];
}

export default function Particles() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const mouseRef = useRef({ x: 0, y: 0 });

  const colors = ['#00D4FF', '#B967FF', '#FF006E', '#39FF14'];

  const createParticle = useCallback((canvas: HTMLCanvasElement): Particle => {
    const maxLife = Math.random() * 300 + 200;
    return {
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      vx: (Math.random() - 0.5) * 0.3,
      vy: Math.random() * 0.3 + 0.1,
      size: Math.random() * 0.8 + 0.2, // Much smaller particles (0.2-1.0px)
      opacity: Math.random() * 0.3 + 0.1, // Much more subtle (0.1-0.4 opacity)
      color: colors[Math.floor(Math.random() * colors.length)],
      life: 0,
      maxLife,
      trail: []
    };
  }, [colors]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();

    const particles: Particle[] = [];

    // Create initial particles (reduced count)
    for (let i = 0; i < 30; i++) {
      particles.push(createParticle(canvas));
    }

    // Mouse interaction
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = { x: e.clientX, y: e.clientY };
    };

    canvas.addEventListener('mousemove', handleMouseMove);

    const animate = () => {
      ctx.fillStyle = 'rgba(10, 10, 15, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      particles.forEach((particle, index) => {
        // Update life
        particle.life++;

        // Reduced mouse interaction
        const dx = mouseRef.current.x - particle.x;
        const dy = mouseRef.current.y - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 100) {
          const force = (100 - distance) / 100;
          particle.vx += (dx / distance) * force * 0.005; // Reduced interaction strength
          particle.vy += (dy / distance) * force * 0.005;
        }

        // Update position with some physics
        particle.vx *= 0.99; // friction
        particle.vy *= 0.99;
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Reduced trail length
        particle.trail.push({
          x: particle.x,
          y: particle.y,
          opacity: particle.opacity
        });

        if (particle.trail.length > 3) { // Shorter trails
          particle.trail.shift();
        }

        // Reset particle if it goes off screen or dies
        if (particle.y > canvas.height + 50 ||
            particle.x < -50 ||
            particle.x > canvas.width + 50 ||
            particle.life > particle.maxLife) {
          particles[index] = createParticle(canvas);
        }

        // Skip trail drawing for smaller particles

        // Draw main particle with minimal glow
        const fadeOpacity = particle.life > particle.maxLife * 0.8
          ? (1 - (particle.life - particle.maxLife * 0.8) / (particle.maxLife * 0.2))
          : 1;

        ctx.globalAlpha = particle.opacity * fadeOpacity;
        ctx.fillStyle = particle.color;
        ctx.shadowBlur = 3; // Reduced glow
        ctx.shadowColor = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();

        ctx.shadowBlur = 0;
      });

      // Remove particle connections to reduce visual noise

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      canvas.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [createParticle]);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ opacity: 0.3 }} // Much more subtle
    />
  );
}